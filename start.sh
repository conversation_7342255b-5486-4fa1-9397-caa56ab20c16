#!/bin/bash

# 数据库查询系统启动/停止脚本 (Linux/macOS)

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
APP_NAME="数据库查询系统"
PID_FILE="/tmp/database_query_system.pid"
LOG_FILE="/tmp/database_query_system.log"
PORT=8080

# 打印横幅
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🔍 数据库查询系统                          ║"
    echo "║                  Database Query System                       ║"
    echo "║                 Linux/macOS 管理脚本                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示使用帮助
show_usage() {
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  $0 start    - 启动服务"
    echo "  $0 stop     - 停止服务"
    echo "  $0 restart  - 重启服务"
    echo "  $0 status   - 查看服务状态"
    echo "  $0 logs     - 查看服务日志"
    echo ""
}

# 检查服务是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 获取进程PID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 检查端口是否被占用
check_port() {
    if command_exists lsof; then
        lsof -i :$PORT > /dev/null 2>&1
    elif command_exists netstat; then
        netstat -ln | grep ":$PORT " > /dev/null 2>&1
    else
        # 如果没有lsof和netstat，尝试连接端口
        timeout 1 bash -c "echo >/dev/tcp/localhost/$PORT" > /dev/null 2>&1
    fi
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查Python
check_python() {
    echo -e "${BLUE}🔍 检查Python环境...${NC}"
    
    if command_exists python3; then
        PYTHON_CMD="python3"
    elif command_exists python; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ 错误: 未找到Python${NC}"
        echo "请先安装Python 3.7或更高版本"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    echo -e "${GREEN}✅ Python已安装: $PYTHON_VERSION${NC}"
}

# 检查pip
check_pip() {
    echo -e "${BLUE}🔍 检查pip...${NC}"
    
    if command_exists pip3; then
        PIP_CMD="pip3"
    elif command_exists pip; then
        PIP_CMD="pip"
    else
        echo -e "${RED}❌ 错误: 未找到pip${NC}"
        echo "请安装pip包管理器"
        exit 1
    fi
    
    echo -e "${GREEN}✅ pip已安装${NC}"
}

# 检查必要文件
check_files() {
    echo -e "${BLUE}📁 检查必要文件...${NC}"
    
    if [ ! -f "app.py" ]; then
        echo -e "${RED}❌ 错误: 未找到app.py文件${NC}"
        exit 1
    fi
    
    if [ ! -f "index.html" ]; then
        echo -e "${RED}❌ 错误: 未找到index.html文件${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 必要文件检查完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 检查并安装依赖包...${NC}"
    
    # 使用清华镜像源安装依赖
    $PIP_CMD install flask flask-cors pymysql tabulate -i https://pypi.tuna.tsinghua.edu.cn/simple --quiet
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 依赖包安装完成${NC}"
    else
        echo -e "${YELLOW}⚠️  依赖包安装可能有问题，但继续启动...${NC}"
    fi
}

# 启动服务
start_service() {
    echo -e "${BLUE}🚀 启动${APP_NAME}...${NC}"

    # 检查是否已经运行
    if is_running; then
        local pid=$(get_pid)
        echo -e "${YELLOW}⚠️  服务已在运行中 (PID: $pid)${NC}"
        echo -e "${BLUE}📍 访问地址: http://localhost:$PORT${NC}"
        return 0
    fi

    # 检查端口是否被占用
    if check_port; then
        echo -e "${RED}❌ 端口 $PORT 已被其他程序占用${NC}"
        echo "请检查是否有其他服务在使用该端口，或修改配置文件中的端口设置"
        return 1
    fi

    # 启动服务
    echo -e "${BLUE}📍 访问地址: http://localhost:$PORT${NC}"
    echo -e "${YELLOW}💡 提示: 服务器启动后会自动打开浏览器${NC}"
    echo -e "${YELLOW}⚠️  使用 $0 stop 可停止服务器${NC}"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""

    # 获取脚本所在目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # 后台启动Python应用
    cd "$script_dir"
    nohup $PYTHON_CMD start.py > "$LOG_FILE" 2>&1 &
    local pid=$!

    # 保存PID
    echo $pid > "$PID_FILE"

    # 等待服务启动
    sleep 3

    # 检查服务是否成功启动
    if is_running; then
        echo -e "${GREEN}✅ 服务启动成功 (PID: $pid)${NC}"
        echo -e "${BLUE}📍 访问地址: http://localhost:$PORT${NC}"
        echo -e "${YELLOW}📋 查看日志: $0 logs${NC}"
        echo -e "${YELLOW}🛑 停止服务: $0 stop${NC}"
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        echo -e "${YELLOW}📋 查看错误日志: tail -f $LOG_FILE${NC}"
        return 1
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}🛑 停止${APP_NAME}...${NC}"

    if ! is_running; then
        echo -e "${YELLOW}⚠️  服务未运行${NC}"
        return 0
    fi

    local pid=$(get_pid)
    echo -e "${BLUE}正在停止服务 (PID: $pid)...${NC}"

    # 尝试优雅停止
    kill "$pid" 2>/dev/null

    # 等待进程结束
    local count=0
    while [ $count -lt 10 ]; do
        if ! is_running; then
            echo -e "${GREEN}✅ 服务已停止${NC}"
            rm -f "$PID_FILE"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    # 强制停止
    echo -e "${YELLOW}⚠️  正在强制停止服务...${NC}"
    kill -9 "$pid" 2>/dev/null
    sleep 2

    if ! is_running; then
        echo -e "${GREEN}✅ 服务已强制停止${NC}"
        rm -f "$PID_FILE"
    else
        echo -e "${RED}❌ 无法停止服务${NC}"
        return 1
    fi
}

# 重启服务
restart_service() {
    echo -e "${BLUE}🔄 重启${APP_NAME}...${NC}"
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}📊 ${APP_NAME}状态:${NC}"
    echo ""

    if is_running; then
        local pid=$(get_pid)
        echo -e "${GREEN}✅ 服务状态: 运行中${NC}"
        echo -e "${BLUE}📍 进程ID: $pid${NC}"
        echo -e "${BLUE}📍 访问地址: http://localhost:$PORT${NC}"
        echo -e "${BLUE}📍 PID文件: $PID_FILE${NC}"
        echo -e "${BLUE}📍 日志文件: $LOG_FILE${NC}"

        # 显示进程信息
        if command_exists ps; then
            echo ""
            echo -e "${YELLOW}进程信息:${NC}"
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || echo "无法获取进程详细信息"
        fi

        # 检查端口监听
        if check_port; then
            echo -e "${GREEN}✅ 端口 $PORT 正在监听${NC}"
        else
            echo -e "${YELLOW}⚠️  端口 $PORT 未在监听${NC}"
        fi
    else
        echo -e "${RED}❌ 服务状态: 未运行${NC}"

        # 检查端口是否被其他程序占用
        if check_port; then
            echo -e "${YELLOW}⚠️  端口 $PORT 被其他程序占用${NC}"
        fi
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}📋 查看${APP_NAME}日志:${NC}"
    echo -e "${YELLOW}日志文件: $LOG_FILE${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo -e "${YELLOW}⚠️  日志文件不存在${NC}"
    fi
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${GREEN}👋 服务器已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        print_banner
        show_usage
        exit 1
    fi

    local action="$1"

    case "$action" in
        "start")
            print_banner
            check_python
            check_pip
            check_files
            install_dependencies
            echo ""
            start_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        *)
            print_banner
            echo -e "${RED}❌ 未知操作: $action${NC}"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# 检查是否在正确的目录
if [ ! -f "start.sh" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录下运行此脚本${NC}"
    exit 1
fi

# 运行主函数
main "$@"
