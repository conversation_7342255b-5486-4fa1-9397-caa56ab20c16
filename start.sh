#!/bin/bash

# 数据库查询系统启动脚本 (Linux/macOS)

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印横幅
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🔍 数据库查询系统                          ║"
    echo "║                  Database Query System                       ║"
    echo "║                   Linux/macOS 启动器                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查Python
check_python() {
    echo -e "${BLUE}🔍 检查Python环境...${NC}"
    
    if command_exists python3; then
        PYTHON_CMD="python3"
    elif command_exists python; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ 错误: 未找到Python${NC}"
        echo "请先安装Python 3.7或更高版本"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    echo -e "${GREEN}✅ Python已安装: $PYTHON_VERSION${NC}"
}

# 检查pip
check_pip() {
    echo -e "${BLUE}🔍 检查pip...${NC}"
    
    if command_exists pip3; then
        PIP_CMD="pip3"
    elif command_exists pip; then
        PIP_CMD="pip"
    else
        echo -e "${RED}❌ 错误: 未找到pip${NC}"
        echo "请安装pip包管理器"
        exit 1
    fi
    
    echo -e "${GREEN}✅ pip已安装${NC}"
}

# 检查必要文件
check_files() {
    echo -e "${BLUE}📁 检查必要文件...${NC}"
    
    if [ ! -f "app.py" ]; then
        echo -e "${RED}❌ 错误: 未找到app.py文件${NC}"
        exit 1
    fi
    
    if [ ! -f "index.html" ]; then
        echo -e "${RED}❌ 错误: 未找到index.html文件${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 必要文件检查完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 检查并安装依赖包...${NC}"
    
    # 使用清华镜像源安装依赖
    $PIP_CMD install flask flask-cors pymysql tabulate -i https://pypi.tuna.tsinghua.edu.cn/simple --quiet
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 依赖包安装完成${NC}"
    else
        echo -e "${YELLOW}⚠️  依赖包安装可能有问题，但继续启动...${NC}"
    fi
}

# 启动服务器
start_server() {
    echo ""
    echo -e "${CYAN}🌐 启动Web服务器...${NC}"
    echo -e "${BLUE}📍 访问地址: http://localhost:8080${NC}"
    echo -e "${YELLOW}💡 提示: 服务器启动后会自动打开浏览器${NC}"
    echo -e "${YELLOW}⚠️  按 Ctrl+C 可停止服务器${NC}"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    
    # 启动Python应用
    $PYTHON_CMD start.py
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${GREEN}👋 服务器已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    print_banner
    
    check_python
    check_pip
    check_files
    install_dependencies
    
    echo ""
    start_server
}

# 检查是否在正确的目录
if [ ! -f "start.sh" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录下运行此脚本${NC}"
    exit 1
fi

# 运行主函数
main
