@echo off
chcp 65001 >nul
title 数据库查询系统启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔍 数据库查询系统                          ║
echo ║                  Database Query System                       ║
echo ║                     Windows 启动器                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 正在启动数据库查询系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
echo.

REM 检查必要文件
if not exist "app.py" (
    echo ❌ 错误: 未找到app.py文件
    pause
    exit /b 1
)

if not exist "index.html" (
    echo ❌ 错误: 未找到index.html文件
    pause
    exit /b 1
)

echo ✅ 必要文件检查完成
echo.

REM 安装依赖（如果需要）
echo 📦 检查并安装依赖包...
pip install flask flask-cors pymysql tabulate -i https://pypi.tuna.tsinghua.edu.cn/simple --quiet

echo.
echo 🌐 启动Web服务器...
echo 📍 访问地址: http://localhost:8080
echo 💡 提示: 服务器启动后会自动打开浏览器
echo ⚠️  按 Ctrl+C 可停止服务器
echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

REM 启动Python应用
python start.py

echo.
echo 👋 服务器已停止
pause
