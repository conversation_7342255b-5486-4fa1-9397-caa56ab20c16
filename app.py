#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API服务器
为Vue前端提供数据库查询接口
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import pymysql
import time
import traceback
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Db1@neiwang',
    'database': 'compony_170',
    'port': 3306,
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def search_monitor_report(search_keyword="美国", limit_count=10,
                         search_fields=None, order_by="createdAt DESC",
                         table_name="MP_TradeControlMonitorReport"):
    """
    参数化查询监控报告表
    支持多关键字搜索（逗号分隔）
    """
    if search_fields is None:
        search_fields = ['title', 'content']

    connection = get_db_connection()
    if not connection:
        raise Exception("无法连接到数据库")

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)  # 使用字典游标

        # 处理多关键字（逗号分隔）
        keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]

        # 构建WHERE子句 - 支持多关键字OR搜索
        all_conditions = []
        params = []

        for keyword in keywords:
            keyword_conditions = []
            like_pattern = f"%{keyword}%"

            for field in search_fields:
                keyword_conditions.append(f"{field} LIKE %s")
                params.append(like_pattern)

            # 每个关键字在所有字段中的OR条件
            if keyword_conditions:
                all_conditions.append(f"({' OR '.join(keyword_conditions)})")

        # 所有关键字之间也是OR关系
        where_clause = " OR ".join(all_conditions) if all_conditions else "1=0"
        params.append(limit_count)

        # 构建完整的SQL查询
        query = f"""
        SELECT *
        FROM {table_name}
        WHERE {where_clause}
        ORDER BY {order_by}
        LIMIT %s
        """

        print(f"执行查询: {query}")
        print(f"参数: {params}")

        cursor.execute(query, params)
        results = cursor.fetchall()

        # 处理日期时间格式和高亮关键字
        for row in results:
            # 先处理日期时间格式
            keys_to_update = list(row.keys())  # 创建键的副本
            for key in keys_to_update:
                value = row[key]
                if isinstance(value, datetime):
                    # 日期格式改为年月日
                    if key in ['MonitorDate']:
                        row[key] = value.strftime('%Y-%m-%d')
                    elif key in ['createdAt']:
                        row[key] = value.strftime('%Y-%m-%d')
                    else:
                        row[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                elif value is None:
                    row[key] = ""

            # 然后添加高亮字段
            for key in ['title', 'content']:
                if key in row and isinstance(row[key], str):
                    row[f"{key}_highlighted"] = highlight_and_truncate(row[key], keywords)

        return results

    finally:
        connection.close()

def highlight_and_truncate(text, keywords, context_length=10):
    """
    高亮关键字并截断文本，只显示关键字前后指定长度的文本
    """
    if not text or not keywords:
        return text

    import re

    # 找到所有关键字的位置
    matches = []
    for keyword in keywords:
        if keyword:
            # 使用正则表达式查找所有匹配位置（忽略大小写）
            for match in re.finditer(re.escape(keyword), text, re.IGNORECASE):
                matches.append((match.start(), match.end(), keyword))

    if not matches:
        return text[:50] + "..." if len(text) > 50 else text

    # 按位置排序
    matches.sort(key=lambda x: x[0])

    # 合并重叠的匹配区域
    merged_segments = []
    for start, end, keyword in matches:
        # 计算上下文范围
        context_start = max(0, start - context_length)
        context_end = min(len(text), end + context_length)

        # 检查是否与已有片段重叠
        merged = False
        for i, (seg_start, seg_end, seg_text) in enumerate(merged_segments):
            if context_start <= seg_end and context_end >= seg_start:
                # 合并重叠片段
                new_start = min(seg_start, context_start)
                new_end = max(seg_end, context_end)
                new_text = text[new_start:new_end]
                merged_segments[i] = (new_start, new_end, new_text)
                merged = True
                break

        if not merged:
            merged_segments.append((context_start, context_end, text[context_start:context_end]))

    # 高亮关键字
    result_segments = []
    for start, end, segment_text in merged_segments:
        highlighted_text = segment_text
        for keyword in keywords:
            if keyword:
                # 使用HTML标记高亮关键字
                pattern = re.compile(re.escape(keyword), re.IGNORECASE)
                highlighted_text = pattern.sub(f'<mark style="background-color: #ffeb3b; padding: 1px 2px; border-radius: 2px;">{keyword}</mark>', highlighted_text)

        # 添加省略号
        prefix = "..." if start > 0 else ""
        suffix = "..." if end < len(text) else ""
        result_segments.append(f"{prefix}{highlighted_text}{suffix}")

    return " | ".join(result_segments)

@app.route('/')
def index():
    """提供前端页面"""
    return send_from_directory('.', 'index.html')

@app.route('/api/query', methods=['POST'])
def api_query():
    """查询API接口"""
    try:
        start_time = time.time()
        
        # 获取请求参数
        data = request.get_json()
        
        search_keyword = data.get('searchKeyword', '美国')
        search_fields = data.get('searchFields', ['title', 'content'])
        limit_count = int(data.get('limitCount', 10))
        order_by = data.get('orderBy', 'createdAt DESC')
        table_name = data.get('tableName', 'MP_TradeControlMonitorReport')
        
        # 参数验证
        if not search_keyword.strip():
            return jsonify({
                'success': False,
                'message': '搜索关键词不能为空',
                'data': []
            }), 400
        
        if limit_count < 1 or limit_count > 100:
            return jsonify({
                'success': False,
                'message': '返回条数必须在1-100之间',
                'data': []
            }), 400
        
        # 执行查询
        results = search_monitor_report(
            search_keyword=search_keyword,
            search_fields=search_fields,
            limit_count=limit_count,
            order_by=order_by,
            table_name=table_name
        )
        
        execution_time = int((time.time() - start_time) * 1000)  # 转换为毫秒
        
        return jsonify({
            'success': True,
            'message': f'查询成功，找到 {len(results)} 条记录',
            'data': results,
            'meta': {
                'total': len(results),
                'execution_time': execution_time,
                'search_keyword': search_keyword,
                'search_fields': search_fields,
                'limit_count': limit_count,
                'order_by': order_by
            }
        })
        
    except Exception as e:
        print(f"查询错误: {e}")
        print(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}',
            'data': []
        }), 500

@app.route('/api/tables', methods=['GET'])
def api_tables():
    """获取数据库表列表"""
    try:
        connection = get_db_connection()
        if not connection:
            raise Exception("无法连接到数据库")
        
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        connection.close()
        
        return jsonify({
            'success': True,
            'data': tables
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取表列表失败: {str(e)}',
            'data': []
        }), 500

@app.route('/api/table-info/<table_name>', methods=['GET'])
def api_table_info(table_name):
    """获取表结构信息"""
    try:
        connection = get_db_connection()
        if not connection:
            raise Exception("无法连接到数据库")
        
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': columns
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取表结构失败: {str(e)}',
            'data': []
        }), 500

@app.route('/api/record/<int:record_id>', methods=['GET'])
def api_get_record(record_id):
    """获取单条记录详细信息"""
    try:
        table_name = request.args.get('table', 'MP_TradeControlMonitorReport')

        connection = get_db_connection()
        if not connection:
            raise Exception("无法连接到数据库")

        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询单条记录
        query = f"SELECT * FROM {table_name} WHERE id = %s"
        cursor.execute(query, (record_id,))
        result = cursor.fetchone()

        if result:
            # 处理日期时间格式
            for key, value in result.items():
                if isinstance(value, datetime):
                    if key in ['MonitorDate']:
                        result[key] = value.strftime('%Y-%m-%d')
                    elif key in ['createdAt']:
                        result[key] = value.strftime('%Y-%m-%d')
                    else:
                        result[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                elif value is None:
                    result[key] = ""
                # 处理日期字符串格式（如果数据库返回的是date类型）
                elif hasattr(value, 'strftime'):
                    if key in ['MonitorDate', 'createdAt']:
                        result[key] = value.strftime('%Y-%m-%d')
                    else:
                        result[key] = str(value)

            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'message': '记录不存在',
                'data': None
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取记录失败: {str(e)}',
            'data': None
        }), 500

    finally:
        if 'connection' in locals():
            connection.close()

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        connection = get_db_connection()
        if connection:
            connection.close()
            return jsonify({
                'success': True,
                'message': '服务正常',
                'database': '连接正常',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据库连接失败',
                'database': '连接异常',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务异常: {str(e)}',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

if __name__ == '__main__':
    print("🚀 启动Flask服务器...")
    print("📊 数据库查询API服务")
    print("🌐 访问地址: http://localhost:8080")
    print("📋 API文档:")
    print("  - POST /api/query - 执行查询")
    print("  - GET  /api/tables - 获取表列表")
    print("  - GET  /api/table-info/<table_name> - 获取表结构")
    print("  - GET  /api/health - 健康检查")
    print("-" * 50)

    app.run(debug=True, host='0.0.0.0', port=8080)
