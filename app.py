#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API服务器
为Vue前端提供数据库查询接口
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import pymysql
import time
import traceback
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Db1@neiwang',
    'database': 'compony_170',
    'port': 3306,
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def search_monitor_report(search_keyword="美国", limit_count=10, 
                         search_fields=None, order_by="createdAt DESC",
                         table_name="MP_TradeControlMonitorReport"):
    """
    参数化查询监控报告表
    """
    if search_fields is None:
        search_fields = ['title', 'content']
    
    connection = get_db_connection()
    if not connection:
        raise Exception("无法连接到数据库")
    
    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)  # 使用字典游标
        
        # 构建WHERE子句
        where_conditions = []
        params = []
        like_pattern = f"%{search_keyword}%"
        
        for field in search_fields:
            where_conditions.append(f"{field} LIKE %s")
            params.append(like_pattern)
        
        where_clause = " OR ".join(where_conditions)
        params.append(limit_count)
        
        # 构建完整的SQL查询
        query = f"""
        SELECT * 
        FROM {table_name} 
        WHERE {where_clause}
        ORDER BY {order_by}
        LIMIT %s
        """
        
        print(f"执行查询: {query}")
        print(f"参数: {params}")
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # 处理日期时间格式
        for row in results:
            for key, value in row.items():
                if isinstance(value, datetime):
                    row[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                elif value is None:
                    row[key] = ""
        
        return results
        
    finally:
        connection.close()

@app.route('/')
def index():
    """提供前端页面"""
    return send_from_directory('.', 'index.html')

@app.route('/api/query', methods=['POST'])
def api_query():
    """查询API接口"""
    try:
        start_time = time.time()
        
        # 获取请求参数
        data = request.get_json()
        
        search_keyword = data.get('searchKeyword', '美国')
        search_fields = data.get('searchFields', ['title', 'content'])
        limit_count = int(data.get('limitCount', 10))
        order_by = data.get('orderBy', 'createdAt DESC')
        table_name = data.get('tableName', 'MP_TradeControlMonitorReport')
        
        # 参数验证
        if not search_keyword.strip():
            return jsonify({
                'success': False,
                'message': '搜索关键词不能为空',
                'data': []
            }), 400
        
        if limit_count < 1 or limit_count > 100:
            return jsonify({
                'success': False,
                'message': '返回条数必须在1-100之间',
                'data': []
            }), 400
        
        # 执行查询
        results = search_monitor_report(
            search_keyword=search_keyword,
            search_fields=search_fields,
            limit_count=limit_count,
            order_by=order_by,
            table_name=table_name
        )
        
        execution_time = int((time.time() - start_time) * 1000)  # 转换为毫秒
        
        return jsonify({
            'success': True,
            'message': f'查询成功，找到 {len(results)} 条记录',
            'data': results,
            'meta': {
                'total': len(results),
                'execution_time': execution_time,
                'search_keyword': search_keyword,
                'search_fields': search_fields,
                'limit_count': limit_count,
                'order_by': order_by
            }
        })
        
    except Exception as e:
        print(f"查询错误: {e}")
        print(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}',
            'data': []
        }), 500

@app.route('/api/tables', methods=['GET'])
def api_tables():
    """获取数据库表列表"""
    try:
        connection = get_db_connection()
        if not connection:
            raise Exception("无法连接到数据库")
        
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        connection.close()
        
        return jsonify({
            'success': True,
            'data': tables
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取表列表失败: {str(e)}',
            'data': []
        }), 500

@app.route('/api/table-info/<table_name>', methods=['GET'])
def api_table_info(table_name):
    """获取表结构信息"""
    try:
        connection = get_db_connection()
        if not connection:
            raise Exception("无法连接到数据库")
        
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': columns
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取表结构失败: {str(e)}',
            'data': []
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        connection = get_db_connection()
        if connection:
            connection.close()
            return jsonify({
                'success': True,
                'message': '服务正常',
                'database': '连接正常',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据库连接失败',
                'database': '连接异常',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务异常: {str(e)}',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

if __name__ == '__main__':
    print("🚀 启动Flask服务器...")
    print("📊 数据库查询API服务")
    print("🌐 访问地址: http://localhost:8080")
    print("📋 API文档:")
    print("  - POST /api/query - 执行查询")
    print("  - GET  /api/tables - 获取表列表")
    print("  - GET  /api/table-info/<table_name> - 获取表结构")
    print("  - GET  /api/health - 健康检查")
    print("-" * 50)

    app.run(debug=True, host='0.0.0.0', port=8080)
