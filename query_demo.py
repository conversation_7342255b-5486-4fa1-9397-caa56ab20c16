#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数化查询演示脚本
演示如何使用参数化查询来安全地查询数据库
"""

import pymysql
from tabulate import tabulate

def connect_mysql(host, user, password, database, port=3306):
    """创建MySQL数据库连接"""
    try:
        conn = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            port=port
        )
        print(f"✅ 成功连接到MySQL数据库: {database}")
        return conn
    except pymysql.Error as e:
        print(f"❌ MySQL连接错误: {e}")
        return None

def search_monitor_report(cursor, table_name="MP_TradeControlMonitorReport", 
                         search_keyword="美国", limit_count=10, 
                         search_fields=None, order_by="createdAt DESC"):
    """
    参数化查询监控报告表
    
    参数:
    - cursor: 数据库游标
    - table_name: 表名
    - search_keyword: 搜索关键词
    - limit_count: 返回记录数限制
    - search_fields: 搜索字段列表，默认为['title', 'content']
    - order_by: 排序字段
    
    返回:
    - results: 查询结果
    - columns_info: 列信息
    """
    if search_fields is None:
        search_fields = ['title', 'content']
    
    # 构建WHERE子句
    where_conditions = []
    params = []
    like_pattern = f"%{search_keyword}%"
    
    for field in search_fields:
        where_conditions.append(f"{field} LIKE %s")
        params.append(like_pattern)
    
    where_clause = " OR ".join(where_conditions)
    params.append(limit_count)
    
    # 构建完整的SQL查询
    query = f"""
    SELECT * 
    FROM {table_name} 
    WHERE {where_clause}
    ORDER BY {order_by}
    LIMIT %s
    """
    
    print(f"🔍 执行查询: 在表 {table_name} 中搜索包含 '{search_keyword}' 的记录")
    print(f"📋 搜索字段: {', '.join(search_fields)}")
    print(f"📊 返回记录数: {limit_count}")
    
    cursor.execute(query, params)
    results = cursor.fetchall()
    
    # 获取列信息
    cursor.execute(f"DESCRIBE {table_name}")
    columns_info = cursor.fetchall()
    
    return results, columns_info

def format_results_for_display(results, columns_info, max_width=50):
    """格式化查询结果用于显示"""
    if not results:
        return [], []
    
    # 获取列名
    column_names = [col[0] for col in columns_info]
    
    # 处理数据，截断长文本
    processed_results = []
    for row in results:
        processed_row = []
        for value in row:
            if value is None:
                processed_row.append("NULL")
            elif isinstance(value, str) and len(value) > max_width:
                # 截断长文本
                processed_row.append(value[:max_width] + "...")
            else:
                processed_row.append(str(value))
        processed_results.append(processed_row)
    
    return processed_results, column_names

def main():
    """主函数 - 演示各种参数化查询"""
    
    # 数据库连接参数
    db_config = {
        'host': '*************',
        'user': 'root',
        'password': 'Db1@neiwang',
        'database': 'compony_170',
        'port': 3306
    }
    
    # 连接数据库
    conn = connect_mysql(**db_config)
    if not conn:
        return
    
    cursor = conn.cursor()
    
    try:
        print("=" * 80)
        print("🚀 参数化查询演示")
        print("=" * 80)
        
        # 演示1: 基本搜索
        print("\n📌 演示1: 搜索包含'美国'的记录")
        results, columns_info = search_monitor_report(
            cursor, 
            search_keyword='美国', 
            limit_count=5
        )
        
        if results:
            processed_results, column_names = format_results_for_display(results, columns_info)
            print(f"\n找到 {len(results)} 条记录:")
            print(tabulate(processed_results, headers=column_names, tablefmt="grid", maxcolwidths=30))
        else:
            print("未找到相关记录")
        
        # 演示2: 指定搜索字段
        print("\n📌 演示2: 只在标题中搜索'参议员'")
        results2, _ = search_monitor_report(
            cursor,
            search_keyword='参议员',
            search_fields=['title'],
            limit_count=3
        )
        
        if results2:
            print(f"\n找到 {len(results2)} 条记录:")
            for i, row in enumerate(results2, 1):
                title = row[8] if len(row) > 8 else "无标题"
                print(f"  {i}. {title}")
        else:
            print("未找到相关记录")
        
        # 演示3: 多关键词搜索
        print("\n📌 演示3: 搜索包含'特朗普'的记录")
        results3, _ = search_monitor_report(
            cursor,
            search_keyword='特朗普',
            limit_count=3,
            order_by='MonitorDate DESC'
        )
        
        if results3:
            print(f"\n找到 {len(results3)} 条记录:")
            for i, row in enumerate(results3, 1):
                title = row[8] if len(row) > 8 else "无标题"
                monitor_date = row[3] if len(row) > 3 else "无日期"
                print(f"  {i}. [{monitor_date}] {title}")
        else:
            print("未找到相关记录")
        
        # 演示4: 自定义查询参数
        print("\n📌 演示4: 自定义查询参数")
        custom_params = {
            'search_keyword': '人工智能',
            'search_fields': ['title', 'content'],
            'limit_count': 2,
            'order_by': 'createdAt DESC'
        }
        
        results4, _ = search_monitor_report(cursor, **custom_params)
        
        if results4:
            print(f"\n找到 {len(results4)} 条包含'{custom_params['search_keyword']}'的记录:")
            for i, row in enumerate(results4, 1):
                title = row[8] if len(row) > 8 else "无标题"
                created_at = row[14] if len(row) > 14 else "无时间"
                print(f"  {i}. [{created_at}] {title}")
        else:
            print(f"未找到包含'{custom_params['search_keyword']}'的记录")
        
    except Exception as e:
        print(f"❌ 查询过程中发生错误: {e}")
    
    finally:
        cursor.close()
        conn.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
