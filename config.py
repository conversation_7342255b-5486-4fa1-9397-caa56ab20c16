#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询系统配置文件
Database Query System Configuration
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': '*************',        # 数据库主机地址
    'user': 'root',                 # 数据库用户名
    'password': 'Db1@neiwang',      # 数据库密码
    'database': 'compony_170',      # 数据库名称
    'port': 3306,                   # 数据库端口
    'charset': 'utf8mb4'            # 字符编码
}

# 服务器配置
SERVER_CONFIG = {
    'host': '0.0.0.0',              # 服务器监听地址 (0.0.0.0表示所有网卡)
    'port': 8080,                   # 服务器端口
    'debug': True,                  # 调试模式 (生产环境请设为False)
    'auto_reload': True             # 自动重载 (开发模式)
}

# 查询配置
QUERY_CONFIG = {
    'default_limit': 10,            # 默认返回记录数
    'max_limit': 100,               # 最大返回记录数
    'default_table': 'MP_TradeControlMonitorReport',  # 默认查询表
    'context_length': 10,           # 关键字前后显示字符数
    'highlight_style': {            # 高亮样式
        'background_color': '#ffeb3b',
        'padding': '1px 2px',
        'border_radius': '2px'
    }
}

# 前端配置
FRONTEND_CONFIG = {
    'auto_open_browser': True,      # 启动时自动打开浏览器
    'browser_delay': 3,             # 延迟打开浏览器的秒数
    'default_search_fields': ['title', 'content'],  # 默认搜索字段
    'default_keywords': '美国,特朗普',  # 默认搜索关键词
    'table_page_size': 10           # 表格分页大小
}

# 安全配置
SECURITY_CONFIG = {
    'enable_cors': True,            # 启用跨域资源共享
    'allowed_origins': ['*'],       # 允许的来源 (生产环境请具体指定)
    'max_content_length': 16 * 1024 * 1024,  # 最大请求内容长度 (16MB)
    'enable_sql_injection_protection': True   # 启用SQL注入保护
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',                # 日志级别: DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'enable_file_logging': False,   # 启用文件日志
    'log_file': 'query_system.log' # 日志文件名
}

# 导出配置
EXPORT_CONFIG = {
    'csv_encoding': 'utf-8-sig',    # CSV文件编码 (utf-8-sig支持Excel)
    'excel_sheet_name': '查询结果',  # Excel工作表名称
    'max_export_records': 1000      # 最大导出记录数
}

# 缓存配置
CACHE_CONFIG = {
    'enable_cache': False,          # 启用查询缓存
    'cache_timeout': 300,           # 缓存超时时间(秒)
    'max_cache_size': 100           # 最大缓存条目数
}

# 字段显示名称映射
FIELD_DISPLAY_NAMES = {
    'id': 'ID',
    'companyId': '公司ID',
    'customer_id': '客户ID',
    'MonitorDate': '监控日期',
    'keywordShow': '关键词显示',
    'keyword': '关键词',
    'keywordRelation': '关键词关系',
    'newsId': '新闻ID',
    'title': '标题',
    'content': '内容',
    'newsSource': '新闻来源',
    'page': '页码',
    'type': '类型',
    'url': '链接地址',
    'createdAt': '创建时间'
}

# 搜索字段选项
SEARCH_FIELD_OPTIONS = [
    {'label': '标题', 'value': 'title'},
    {'label': '内容', 'value': 'content'},
    {'label': '关键词', 'value': 'keyword'},
    {'label': '新闻来源', 'value': 'newsSource'},
    {'label': '关键词显示', 'value': 'keywordShow'},
    {'label': '关键词关系', 'value': 'keywordRelation'}
]

# 排序选项
ORDER_BY_OPTIONS = [
    {'label': '创建时间 (降序)', 'value': 'createdAt DESC'},
    {'label': '创建时间 (升序)', 'value': 'createdAt ASC'},
    {'label': '监控日期 (降序)', 'value': 'MonitorDate DESC'},
    {'label': '监控日期 (升序)', 'value': 'MonitorDate ASC'},
    {'label': 'ID (降序)', 'value': 'id DESC'},
    {'label': 'ID (升序)', 'value': 'id ASC'}
]

# 数据库表选项
TABLE_OPTIONS = [
    {
        'name': 'MP_TradeControlMonitorReport',
        'display_name': '贸易管制监控报告',
        'description': '包含贸易管制相关的监控报告数据'
    },
    {
        'name': 'MP_CompanyInfo',
        'display_name': '公司信息',
        'description': '公司基本信息数据'
    }
]

def get_database_url():
    """获取数据库连接URL"""
    config = DATABASE_CONFIG
    return f"mysql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"

def get_server_url():
    """获取服务器访问URL"""
    config = SERVER_CONFIG
    host = 'localhost' if config['host'] == '0.0.0.0' else config['host']
    return f"http://{host}:{config['port']}"

def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 验证数据库配置
    if not DATABASE_CONFIG.get('host'):
        errors.append("数据库主机地址不能为空")
    
    if not DATABASE_CONFIG.get('user'):
        errors.append("数据库用户名不能为空")
    
    if not DATABASE_CONFIG.get('database'):
        errors.append("数据库名称不能为空")
    
    # 验证服务器配置
    port = SERVER_CONFIG.get('port')
    if not isinstance(port, int) or port < 1 or port > 65535:
        errors.append("服务器端口必须是1-65535之间的整数")
    
    # 验证查询配置
    if QUERY_CONFIG.get('max_limit', 0) < QUERY_CONFIG.get('default_limit', 0):
        errors.append("最大返回记录数不能小于默认返回记录数")
    
    return errors

if __name__ == "__main__":
    # 配置验证
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
        print(f"📊 数据库: {get_database_url()}")
        print(f"🌐 服务器: {get_server_url()}")
