# 🚀 启动脚本使用说明

## 📋 概述

`start.sh` 是数据库查询系统的管理脚本，支持启动、停止、重启、状态查看和日志查看等功能。

## 🎯 支持的操作

### 1. 启动服务
```bash
./start.sh start
```
**功能**：
- 检查Python环境和依赖
- 检查必要文件
- 自动安装缺失的依赖包
- 后台启动服务
- 自动打开浏览器

**输出示例**：
```
🚀 启动数据库查询系统...
📍 访问地址: http://localhost:8080
✅ 服务启动成功 (PID: 1234)
```

### 2. 停止服务
```bash
./start.sh stop
```
**功能**：
- 优雅停止正在运行的服务
- 如果优雅停止失败，会强制停止
- 清理PID文件

**输出示例**：
```
🛑 停止数据库查询系统...
正在停止服务 (PID: 1234)...
✅ 服务已停止
```

### 3. 重启服务
```bash
./start.sh restart
```
**功能**：
- 先停止服务
- 等待2秒
- 重新启动服务

### 4. 查看服务状态
```bash
./start.sh status
```
**功能**：
- 显示服务运行状态
- 显示进程ID和访问地址
- 显示进程详细信息
- 检查端口监听状态

**输出示例**：
```
📊 数据库查询系统状态:

✅ 服务状态: 运行中
📍 进程ID: 1234
📍 访问地址: http://localhost:8080
📍 PID文件: /tmp/database_query_system.pid
📍 日志文件: /tmp/database_query_system.log

进程信息:
  PID  PPID ELAPSED  %CPU %MEM
 1234     1   00:05   0.0  0.1

✅ 端口 8080 正在监听
```

### 5. 查看日志
```bash
./start.sh logs
```
**功能**：
- 实时查看服务日志
- 使用 `Ctrl+C` 退出日志查看

## 📁 相关文件

### 临时文件位置
- **PID文件**: `/tmp/database_query_system.pid`
- **日志文件**: `/tmp/database_query_system.log`

### 配置信息
- **服务名称**: 数据库查询系统
- **默认端口**: 8080
- **访问地址**: http://localhost:8080

## 🔧 故障排除

### 1. 服务启动失败
**可能原因**：
- 端口被占用
- Python环境问题
- 依赖包缺失
- 数据库连接失败

**解决方法**：
```bash
# 查看详细错误日志
tail -f /tmp/database_query_system.log

# 检查端口占用
lsof -i :8080

# 手动安装依赖
pip install flask flask-cors pymysql tabulate -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 服务无法停止
**解决方法**：
```bash
# 查看进程
ps aux | grep start.py

# 手动强制停止
kill -9 <PID>

# 清理PID文件
rm -f /tmp/database_query_system.pid
```

### 3. 端口被占用
**解决方法**：
```bash
# 查看端口占用
lsof -i :8080

# 停止占用端口的进程
kill -9 <PID>

# 或者修改配置文件中的端口
```

## 💡 使用技巧

### 1. 快速重启
```bash
./start.sh restart
```

### 2. 检查服务是否正常
```bash
./start.sh status
curl http://localhost:8080/api/health
```

### 3. 后台运行监控
```bash
# 启动服务
./start.sh start

# 监控日志
./start.sh logs
```

### 4. 开机自启动
可以将启动命令添加到系统启动脚本中：

**Linux (systemd)**：
```bash
# 创建服务文件
sudo nano /etc/systemd/system/database-query.service

# 添加以下内容：
[Unit]
Description=Database Query System
After=network.target

[Service]
Type=forking
User=your_username
WorkingDirectory=/path/to/your/project
ExecStart=/path/to/your/project/start.sh start
ExecStop=/path/to/your/project/start.sh stop
Restart=always

[Install]
WantedBy=multi-user.target

# 启用服务
sudo systemctl enable database-query.service
sudo systemctl start database-query.service
```

**macOS (launchd)**：
```bash
# 创建plist文件
nano ~/Library/LaunchAgents/com.database.query.plist

# 添加相应配置
```

## 🚨 注意事项

1. **权限问题**：确保脚本有执行权限
   ```bash
   chmod +x start.sh
   ```

2. **路径问题**：必须在项目根目录下运行脚本
   ```bash
   cd /path/to/database_conn
   ./start.sh start
   ```

3. **Python版本**：需要Python 3.7或更高版本

4. **网络访问**：确保能访问PyPI镜像源进行依赖安装

5. **数据库连接**：确保数据库服务正常运行且网络可达

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件：`./start.sh logs`
2. 检查服务状态：`./start.sh status`
3. 查看详细错误：`tail -f /tmp/database_query_system.log`
4. 检查系统资源：`top` 或 `htop`

---

**版本**: v1.0
**更新日期**: 2025-07-23
**兼容系统**: Linux, macOS
