# 🔍 数据库查询系统

一个基于Vue.js + Flask的美观数据库查询系统，支持参数化查询和实时数据展示。

## ✨ 功能特性

### 🎯 核心功能
- **参数化查询**: 安全的SQL参数化查询，防止SQL注入
- **多关键字搜索**: 支持逗号分隔的多关键字搜索，关键字间为OR关系
- **智能高亮**: 自动高亮搜索关键字，只显示关键字前后10个字符
- **多字段搜索**: 支持在标题、内容、关键词等多个字段中搜索
- **实时结果**: 即时显示查询结果，支持排序和分页
- **Excel导出**: 支持单条记录的Excel格式查看和导出
- **美观界面**: 现代化的Vue.js界面，响应式设计

### 🛡️ 安全特性
- SQL参数化查询防止注入攻击
- CORS跨域支持
- 错误处理和异常捕获
- 输入验证和参数校验

### 🎨 界面特色
- 渐变背景和毛玻璃效果
- 响应式表格设计
- 实时加载状态
- 统计卡片展示
- 优雅的错误提示

## 🚀 快速开始

### 环境要求
- Python 3.7+
- MySQL 5.7+
- 现代浏览器

### 安装依赖

```bash
# 安装Python依赖
pip install pymysql flask flask-cors tabulate -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 配置数据库

编辑 `app.py` 中的数据库配置：

```python
DB_CONFIG = {
    'host': '*************',     # 数据库主机
    'user': 'root',              # 用户名
    'password': 'Db1@neiwang',   # 密码
    'database': 'compony_170',   # 数据库名
    'port': 3306,                # 端口
    'charset': 'utf8mb4'
}
```

### 启动服务

```bash
# 启动Flask后端服务
python3 app.py
```

服务启动后访问: http://localhost:8080

## 📋 API接口

### POST /api/query
执行数据库查询

**请求参数:**
```json
{
    "searchKeyword": "美国",
    "searchFields": ["title", "content"],
    "limitCount": 10,
    "orderBy": "createdAt DESC",
    "tableName": "MP_TradeControlMonitorReport"
}
```

**响应格式:**
```json
{
    "success": true,
    "message": "查询成功，找到 10 条记录",
    "data": [...],
    "meta": {
        "total": 10,
        "execution_time": 150,
        "search_keyword": "美国",
        "search_fields": ["title", "content"],
        "limit_count": 10,
        "order_by": "createdAt DESC"
    }
}
```

### GET /api/tables
获取数据库表列表

### GET /api/table-info/<table_name>
获取指定表的结构信息

### GET /api/health
健康检查接口

## 🎛️ 使用说明

### 查询参数设置

1. **搜索关键词**: 输入要搜索的关键词，支持多关键词用逗号分隔（如：美国,特朗普）
2. **搜索字段**: 选择要搜索的字段（标题、内容、关键词、新闻来源）
3. **返回条数**: 设置返回结果的数量限制（1-100）
4. **排序字段**: 选择结果排序方式
5. **表名**: 指定要查询的数据表

### 查询结果展示

- **统计卡片**: 显示查询结果数量、关键词、搜索字段数、执行时间
- **数据表格**: 以表格形式展示查询结果，支持关键字高亮
- **字段说明**:
  - ID: 记录唯一标识
  - 标题: 新闻标题（高亮显示匹配关键词）
  - 内容预览: 智能截断，只显示关键词前后10个字符（高亮显示匹配关键词）
  - 关键词: 监控关键词
  - 监控日期: 数据监控日期（年月日格式）
  - 来源: 新闻来源
  - 创建时间: 记录创建时间（年月日格式）
  - 操作: 查看详情（Excel格式）、查看原文链接

### Excel格式详情查看

- **完整数据**: 显示记录的所有字段信息
- **Excel样式**: 仿Excel表格样式，清晰易读
- **字段高亮**: 重要字段（标题、内容、关键词）特殊高亮
- **导出功能**: 支持导出为CSV格式文件
- **长文本处理**: 长文本字段支持滚动查看

## 📁 文件结构

```
database_conn/
├── app.py              # Flask后端服务
├── index.html          # Vue前端页面
├── query_demo.py       # 查询演示脚本
├── database-doo        # 数据库连接工具
└── README.md          # 说明文档
```

## 🔧 技术栈

### 后端
- **Flask**: Python Web框架
- **PyMySQL**: MySQL数据库连接器
- **Flask-CORS**: 跨域资源共享

### 前端
- **Vue.js 3**: 渐进式JavaScript框架
- **Element Plus**: Vue 3组件库
- **Axios**: HTTP客户端

### 数据库
- **MySQL 5.7+**: 关系型数据库

## 🎨 界面预览

### 查询界面
- 现代化的参数设置表单
- 实时查询状态显示
- 美观的统计卡片

### 结果展示
- 响应式数据表格
- 智能内容截断
- 优雅的加载动画

## 🛠️ 开发说明

### 参数化查询实现

```python
def search_monitor_report(search_keyword, search_fields, limit_count, order_by):
    # 构建WHERE子句
    where_conditions = []
    params = []
    like_pattern = f"%{search_keyword}%"
    
    for field in search_fields:
        where_conditions.append(f"{field} LIKE %s")
        params.append(like_pattern)
    
    where_clause = " OR ".join(where_conditions)
    params.append(limit_count)
    
    # 执行参数化查询
    query = f"SELECT * FROM table WHERE {where_clause} LIMIT %s"
    cursor.execute(query, params)
```

### 前端数据处理

```javascript
// API调用
const response = await axios.post('/api/query', queryParams);

// 结果处理
if (response.data.success) {
    this.queryResults = response.data.data;
    this.executionTime = response.data.meta.execution_time;
}
```

## 📝 注意事项

1. **安全性**: 系统使用参数化查询防止SQL注入
2. **性能**: 建议设置合理的查询限制条数
3. **网络**: 确保数据库网络连接正常
4. **浏览器**: 推荐使用Chrome、Firefox等现代浏览器

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
