# 导入所需的数据库模块
import sqlite3
import pymysql
from tabulate import tabulate
# import psycopg2  # PostgreSQL - 暂时注释掉
# import pyodbc    # SQL Server - 暂时注释掉


def connect_sqlite(db_file):
    """创建SQLite数据库连接"""
    try:
        conn = sqlite3.connect(db_file)
        print(f"成功连接到SQLite数据库: {db_file}")
        return conn
    except sqlite3.Error as e:
        print(f"SQLite连接错误: {e}")
        return None


def connect_mysql(host, user, password, database, port=3306):
    """创建MySQL数据库连接"""
    try:
        conn = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            port=port
        )
        print(f"成功连接到MySQL数据库: {database}")
        return conn
    except pymysql.Error as e:
        print(f"MySQL连接错误: {e}")
        return None


def connect_postgresql(host, user, password, database, port=5432):
    """创建PostgreSQL数据库连接"""
    try:
        conn = psycopg2.connect(
            host=host,
            user=user,
            password=password,
            dbname=database,
            port=port
        )
        print(f"成功连接到PostgreSQL数据库: {database}")
        return conn
    except psycopg2.Error as e:
        print(f"PostgreSQL连接错误: {e}")
        return None


def connect_mssql(server, database, username=None, password=None, trusted_connection=False):
    """创建Microsoft SQL Server数据库连接"""
    try:
        if trusted_connection:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;"
        else:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"

        conn = pyodbc.connect(conn_str)
        print(f"成功连接到MSSQL数据库: {database}")
        return conn
    except pyodbc.Error as e:
        print(f"MSSQL连接错误: {e}")
        return None


def search_monitor_report(cursor, table_name="MP_TradeControlMonitorReport",
                         search_keyword="美国", limit_count=10,
                         search_fields=None, order_by="createdAt DESC"):
    """
    参数化查询监控报告表

    参数:
    - cursor: 数据库游标
    - table_name: 表名
    - search_keyword: 搜索关键词
    - limit_count: 返回记录数限制
    - search_fields: 搜索字段列表，默认为['title', 'content']
    - order_by: 排序字段

    返回:
    - results: 查询结果
    - columns_info: 列信息
    """
    if search_fields is None:
        search_fields = ['title', 'content']

    # 构建WHERE子句
    where_conditions = []
    params = []
    like_pattern = f"%{search_keyword}%"

    for field in search_fields:
        where_conditions.append(f"{field} LIKE %s")
        params.append(like_pattern)

    where_clause = " OR ".join(where_conditions)
    params.append(limit_count)

    # 构建完整的SQL查询
    query = f"""
    SELECT *
    FROM {table_name}
    WHERE {where_clause}
    ORDER BY {order_by}
    LIMIT %s
    """

    print(f"执行查询: 在表 {table_name} 中搜索包含 '{search_keyword}' 的记录")
    print(f"搜索字段: {', '.join(search_fields)}")
    print(f"返回记录数: {limit_count}")

    cursor.execute(query, params)
    results = cursor.fetchall()

    # 获取列信息
    cursor.execute(f"DESCRIBE {table_name}")
    columns_info = cursor.fetchall()

    return results, columns_info


# 使用示例
def main():
    # SQLite连接示例
    sqlite_conn = connect_sqlite("example.db")
    if sqlite_conn:
        # 执行一些操作
        cursor = sqlite_conn.cursor()
        cursor.execute("CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY, name TEXT, age INTEGER)")
        sqlite_conn.commit()
        sqlite_conn.close()
    
    # MySQL连接示例
    mysql_conn = connect_mysql("192.168.1.120", "root", "Db1@neiwang", "compony_170", 3306)
    if mysql_conn:
        # 执行一些操作
        cursor = mysql_conn.cursor()
        # 显示数据库中的所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("数据库中的表:")
        for table in tables:
            print(f"  - {table[0]}")

        # 获取数据库信息
        cursor.execute("SELECT DATABASE(), VERSION(), USER()")
        db_info = cursor.fetchone()
        print(f"\n数据库信息:")
        print(f"  当前数据库: {db_info[0]}")
        print(f"  MySQL版本: {db_info[1]}")
        print(f"  当前用户: {db_info[2]}")

        # 使用参数化查询函数
        # 可以修改这些参数来自定义查询
        search_params = {
            'search_keyword': '美国',
            'limit_count': 10,
            'search_fields': ['title', 'content'],  # 可以添加更多字段
            'order_by': 'createdAt DESC'
        }

        print(f"\n=== 参数化查询示例 ===")
        results, columns_info = search_monitor_report(cursor, **search_params)

        # 使用表格显示字段信息
        print(f"\n表结构信息:")
        table_headers = ["字段名", "数据类型", "是否为空", "键", "默认值", "额外信息"]
        print(tabulate(columns_info, headers=table_headers, tablefmt="grid"))

        if results:
            print(f"\n找到 {len(results)} 条包含'{search_params['search_keyword']}'的记录:")

            # 获取列名
            column_names = [col[0] for col in columns_info]

            # 处理数据，截断长文本
            processed_results = []
            for row in results:
                processed_row = []
                for value in row:
                    if value is None:
                        processed_row.append("NULL")
                    elif isinstance(value, str) and len(value) > 50:
                        # 截断长文本，保留前50个字符
                        processed_row.append(value[:50] + "...")
                    else:
                        processed_row.append(str(value))
                processed_results.append(processed_row)

            # 使用表格格式输出
            print(tabulate(processed_results, headers=column_names, tablefmt="grid", maxcolwidths=50))
        else:
            print(f"未找到包含'{search_params['search_keyword']}'的记录")

        # 演示不同参数的查询
        print(f"\n=== 其他查询示例 ===")

        # 示例1: 搜索不同关键词
        print(f"\n1. 搜索包含'特朗普'的记录:")
        results2, _ = search_monitor_report(cursor, search_keyword='特朗普', limit_count=3)
        if results2:
            print(f"找到 {len(results2)} 条记录")
            # 只显示标题
            for i, row in enumerate(results2, 1):
                title = row[8] if len(row) > 8 else "无标题"  # title字段在第9列(索引8)
                print(f"  {i}. {title}")
        else:
            print("未找到相关记录")

        # 示例2: 只搜索标题字段
        print(f"\n2. 只在标题中搜索'参议员':")
        results3, _ = search_monitor_report(cursor, search_keyword='参议员',
                                          search_fields=['title'], limit_count=3)
        if results3:
            print(f"找到 {len(results3)} 条记录")
            for i, row in enumerate(results3, 1):
                title = row[8] if len(row) > 8 else "无标题"
                print(f"  {i}. {title}")
        else:
            print("未找到相关记录")

        mysql_conn.close()

if __name__ == "__main__":
    main()