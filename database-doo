# 导入所需的数据库模块
import sqlite3
import pymysql
# import psycopg2  # PostgreSQL - 暂时注释掉
# import pyodbc    # SQL Server - 暂时注释掉


def connect_sqlite(db_file):
    """创建SQLite数据库连接"""
    try:
        conn = sqlite3.connect(db_file)
        print(f"成功连接到SQLite数据库: {db_file}")
        return conn
    except sqlite3.Error as e:
        print(f"SQLite连接错误: {e}")
        return None


def connect_mysql(host, user, password, database, port=3306):
    """创建MySQL数据库连接"""
    try:
        conn = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            port=port
        )
        print(f"成功连接到MySQL数据库: {database}")
        return conn
    except pymysql.Error as e:
        print(f"MySQL连接错误: {e}")
        return None


def connect_postgresql(host, user, password, database, port=5432):
    """创建PostgreSQL数据库连接"""
    try:
        conn = psycopg2.connect(
            host=host,
            user=user,
            password=password,
            dbname=database,
            port=port
        )
        print(f"成功连接到PostgreSQL数据库: {database}")
        return conn
    except psycopg2.Error as e:
        print(f"PostgreSQL连接错误: {e}")
        return None


def connect_mssql(server, database, username=None, password=None, trusted_connection=False):
    """创建Microsoft SQL Server数据库连接"""
    try:
        if trusted_connection:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;"
        else:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
        
        conn = pyodbc.connect(conn_str)
        print(f"成功连接到MSSQL数据库: {database}")
        return conn
    except pyodbc.Error as e:
        print(f"MSSQL连接错误: {e}")
        return None


# 使用示例
def main():
    # SQLite连接示例
    sqlite_conn = connect_sqlite("example.db")
    if sqlite_conn:
        # 执行一些操作
        cursor = sqlite_conn.cursor()
        cursor.execute("CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY, name TEXT, age INTEGER)")
        sqlite_conn.commit()
        sqlite_conn.close()
    
    # MySQL连接示例
    mysql_conn = connect_mysql("192.168.1.120", "root", "Db1@neiwang", "compony_170", 3306)
    if mysql_conn:
        # 执行一些操作
        cursor = mysql_conn.cursor()
        # 显示数据库中的所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("数据库中的表:")
        for table in tables:
            print(f"  - {table[0]}")

        # 获取数据库信息
        cursor.execute("SELECT DATABASE(), VERSION(), USER()")
        db_info = cursor.fetchone()
        print(f"\n数据库信息:")
        print(f"  当前数据库: {db_info[0]}")
        print(f"  MySQL版本: {db_info[1]}")
        print(f"  当前用户: {db_info[2]}")

        # 先查看MP_TradeControlMonitorReport表的结构
        print(f"\n查看MP_TradeControlMonitorReport表结构:")
        cursor.execute("DESCRIBE MP_TradeControlMonitorReport")
        columns = cursor.fetchall()
        print("表字段:")
        for col in columns:
            print(f"  {col[0]} - {col[1]}")

        # 查询MP_TradeControlMonitorReport表中title或content包含"美国"的数据
        print(f"\n查询MP_TradeControlMonitorReport表中包含'美国'的数据:")
        # 先查询所有字段，然后根据实际字段调整
        query = """
        SELECT *
        FROM MP_TradeControlMonitorReport
        WHERE title LIKE '%美国%' OR content LIKE '%美国%'
        LIMIT 5
        """
        cursor.execute(query)
        results = cursor.fetchall()

        if results:
            print(f"找到 {len(results)} 条包含'美国'的记录:")
            for i, row in enumerate(results, 1):
                print(f"\n记录 {i}:")
                # 显示所有字段的值
                for j, col in enumerate(columns):
                    value = row[j] if j < len(row) else "N/A"
                    # 如果是长文本字段，只显示前100个字符
                    if col[0] in ['content', 'title'] and value and len(str(value)) > 100:
                        value = str(value)[:100] + "..."
                    print(f"  {col[0]}: {value}")
        else:
            print("未找到包含'美国'的记录")

        mysql_conn.close()

if __name__ == "__main__":
    main()