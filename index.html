<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库查询系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .query-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: end;
        }
        
        .form-item {
            flex: 1;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        }
        
        .loading-container {
            text-align: center;
            padding: 50px;
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }
        
        .el-table {
            border-radius: 15px;
        }
        
        .el-table th {
            background: #f8f9fa !important;
            color: #2c3e50 !important;
            font-weight: 600;
        }
        
        .el-table td {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .el-table tr:hover {
            background: #f8f9fa !important;
        }
        
        .content-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .keyword-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        
        .date-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.85em;
        }
        
        .url-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }

        /* Excel样式表格 */
        .detail-container {
            max-height: 60vh;
            overflow-y: auto;
        }

        .excel-table {
            width: 100%;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            overflow: hidden;
        }

        .excel-style-table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
        }

        .excel-style-table th {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            color: #24292f;
        }

        .excel-style-table td {
            border: 1px solid #d0d7de;
            padding: 12px 16px;
            vertical-align: top;
        }

        .excel-style-table tr:nth-child(even) {
            background: #f6f8fa;
        }

        .excel-style-table tr:hover {
            background: #fff8e1;
        }

        .field-name {
            font-weight: 600;
            color: #0969da;
            background: #f1f8ff !important;
            white-space: nowrap;
        }

        .field-value {
            word-break: break-all;
            line-height: 1.5;
        }

        .long-text {
            max-height: 100px;
            overflow-y: auto;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #0969da;
        }

        .url-cell a {
            color: #0969da;
            text-decoration: none;
            word-break: break-all;
        }

        .url-cell a:hover {
            text-decoration: underline;
        }

        .highlight-row {
            background: #fff3cd !important;
        }

        .highlight-row:hover {
            background: #ffeaa7 !important;
        }

        .dialog-footer {
            text-align: right;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>🔍 数据库查询系统</h1>
                <p>智能化参数查询，美观数据展示</p>
            </div>
            
            <!-- 查询表单 -->
            <div class="query-form">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">
                    <el-icon><Search /></el-icon>
                    查询参数设置
                </h3>
                
                <div class="form-row">
                    <div class="form-item">
                        <el-form-item label="搜索关键词">
                            <el-input
                                v-model="queryParams.searchKeyword"
                                placeholder="请输入搜索关键词，多个关键词用逗号分隔"
                                clearable
                                prefix-icon="Search">
                                <template #append>
                                    <el-tooltip content="支持多关键词搜索，用逗号分隔，如：美国,特朗普" placement="top">
                                        <el-icon><QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </template>
                            </el-input>
                        </el-form-item>
                    </div>
                    
                    <div class="form-item">
                        <el-form-item label="搜索字段">
                            <el-select 
                                v-model="queryParams.searchFields" 
                                multiple 
                                placeholder="选择搜索字段"
                                style="width: 100%">
                                <el-option label="标题" value="title"></el-option>
                                <el-option label="内容" value="content"></el-option>
                                <el-option label="关键词" value="keyword"></el-option>
                                <el-option label="新闻来源" value="newsSource"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    
                    <div class="form-item" style="flex: 0.5;">
                        <el-form-item label="返回条数">
                            <el-input-number 
                                v-model="queryParams.limitCount" 
                                :min="1" 
                                :max="100"
                                style="width: 100%">
                            </el-input-number>
                        </el-form-item>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-item">
                        <el-form-item label="排序字段">
                            <el-select v-model="queryParams.orderBy" placeholder="选择排序方式">
                                <el-option label="创建时间 (降序)" value="createdAt DESC"></el-option>
                                <el-option label="创建时间 (升序)" value="createdAt ASC"></el-option>
                                <el-option label="监控日期 (降序)" value="MonitorDate DESC"></el-option>
                                <el-option label="监控日期 (升序)" value="MonitorDate ASC"></el-option>
                                <el-option label="ID (降序)" value="id DESC"></el-option>
                                <el-option label="ID (升序)" value="id ASC"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    
                    <div class="form-item">
                        <el-form-item label="表名">
                            <el-input 
                                v-model="queryParams.tableName" 
                                placeholder="数据表名称"
                                readonly>
                            </el-input>
                        </el-form-item>
                    </div>
                    
                    <div class="form-item" style="flex: 0.3;">
                        <el-button 
                            type="primary" 
                            @click="executeQuery" 
                            :loading="loading"
                            size="large"
                            style="width: 100%; background: linear-gradient(45deg, #667eea, #764ba2); border: none;">
                            <el-icon><Search /></el-icon>
                            {{ loading ? '查询中...' : '执行查询' }}
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div v-if="queryResults.length > 0" class="stats-cards">
                <div class="stat-card">
                    <div class="stat-number">{{ queryResults.length }}</div>
                    <div class="stat-label">查询结果</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ queryParams.searchKeyword }}</div>
                    <div class="stat-label">搜索关键词</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ queryParams.searchFields.length }}</div>
                    <div class="stat-label">搜索字段</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ formatExecutionTime }}</div>
                    <div class="stat-label">执行时间</div>
                </div>
            </div>
            
            <!-- 查询结果 -->
            <div class="results-section">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-container">
                    <el-icon class="is-loading" size="50" color="#667eea"><Loading /></el-icon>
                    <p style="margin-top: 20px; color: #667eea; font-size: 1.1em;">正在查询数据...</p>
                </div>
                
                <!-- 无数据状态 -->
                <div v-else-if="!queryResults.length && hasSearched" class="no-data">
                    <el-icon size="80" color="#bdc3c7"><DocumentRemove /></el-icon>
                    <h3 style="color: #7f8c8d; margin: 20px 0;">未找到匹配的数据</h3>
                    <p style="color: #95a5a6;">请尝试调整搜索条件或关键词</p>
                </div>
                
                <!-- 数据表格 -->
                <div v-else-if="queryResults.length > 0" class="table-container">
                    <el-table 
                        :data="queryResults" 
                        style="width: 100%"
                        :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
                        stripe>
                        
                        <el-table-column prop="id" label="ID" width="80" align="center">
                            <template #default="scope">
                                <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="title" label="标题" min-width="200">
                            <template #default="scope">
                                <div style="font-weight: 500; color: #2c3e50;" v-html="scope.row.title_highlighted || scope.row.title">
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column prop="content" label="内容预览" min-width="250">
                            <template #default="scope">
                                <div class="content-preview" :title="scope.row.content" v-html="scope.row.content_highlighted || scope.row.content">
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="keyword" label="关键词" width="120">
                            <template #default="scope">
                                <span class="keyword-tag">{{ scope.row.keyword }}</span>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="MonitorDate" label="监控日期" width="120">
                            <template #default="scope">
                                <span class="date-badge">{{ scope.row.MonitorDate }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="newsSource" label="来源" width="100">
                            <template #default="scope">
                                <el-tag v-if="scope.row.newsSource" size="small" type="success">
                                    {{ scope.row.newsSource }}
                                </el-tag>
                                <span v-else style="color: #bdc3c7;">-</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="createdAt" label="创建时间" width="120">
                            <template #default="scope">
                                <el-icon><Clock /></el-icon>
                                {{ scope.row.createdAt }}
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="150" align="center">
                            <template #default="scope">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="viewRecord(scope.row)"
                                    style="margin-right: 5px;">
                                    <el-icon><View /></el-icon>
                                    查看详情
                                </el-button>
                                <el-button
                                    v-if="scope.row.url"
                                    type="success"
                                    size="small"
                                    @click="openUrl(scope.row.url)"
                                    link>
                                    <el-icon><Link /></el-icon>
                                    原文
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <!-- 详情查看对话框 -->
            <el-dialog
                v-model="detailDialogVisible"
                title="记录详情"
                width="80%"
                :before-close="handleDetailClose">

                <div v-if="currentRecord" class="detail-container">
                    <!-- Excel样式的表格 -->
                    <div class="excel-table">
                        <table class="excel-style-table">
                            <thead>
                                <tr>
                                    <th style="width: 150px;">字段名</th>
                                    <th>字段值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(value, key) in currentRecord" :key="key"
                                    :class="{ 'highlight-row': isHighlightField(key) }">
                                    <td class="field-name">{{ getFieldDisplayName(key) }}</td>
                                    <td class="field-value">
                                        <div v-if="key === 'url' && value" class="url-cell">
                                            <a :href="value" target="_blank" class="url-link">{{ value }}</a>
                                        </div>
                                        <div v-else-if="isLongText(value)" class="long-text">
                                            {{ value }}
                                        </div>
                                        <div v-else>
                                            {{ value || '-' }}
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="detailDialogVisible = false">关闭</el-button>
                        <el-button type="primary" @click="exportToExcel">
                            <el-icon><Download /></el-icon>
                            导出Excel
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    hasSearched: false,
                    executionTime: 0,
                    detailDialogVisible: false,
                    currentRecord: null,
                    queryParams: {
                        searchKeyword: '美国,特朗普',
                        searchFields: ['title', 'content'],
                        limitCount: 10,
                        orderBy: 'createdAt DESC',
                        tableName: 'MP_TradeControlMonitorReport'
                    },
                    queryResults: [],
                    // 模拟数据
                    mockData: [
                        {
                            id: 12,
                            companyId: 158,
                            customer_id: 22,
                            MonitorDate: '2025-07-20',
                            keywordShow: '贾茹',
                            keyword: '贾茹',
                            keywordRelation: 'aa',
                            newsId: '1',
                            title: '参议员布鲁门撒尔：一周回顾（4月18日至4月25日）',
                            content: '文章讨论了特朗普总统利用行政命令打击其认为的敌人，特别是针对不支持其代理律师托德·布兰奇的律所。这些命令被认为是对美国法治的威胁...',
                            newsSource: '参议院官网',
                            page: 3,
                            type: 2,
                            url: 'https://www.blumenthal.senate.gov/newsroom/press/release',
                            createdAt: '2025-04-26 06:27:39'
                        },
                        {
                            id: 13,
                            companyId: 158,
                            customer_id: 22,
                            MonitorDate: '2025-07-19',
                            keywordShow: '君正集成电路',
                            keyword: '君正集成电路',
                            keywordRelation: 'bb',
                            newsId: '2',
                            title: '参议员布鲁门撒尔：一周回顾（4月18日至4月25日）',
                            content: '文章主要讨论了特朗普总统发布的一系列行政命令，这些命令针对特定律师事务所，试图通过恐吓和限制其法律代表来削弱美国的司法独立性...',
                            newsSource: '政府网站',
                            page: 3,
                            type: 2,
                            url: 'https://www.blumenthal.senate.gov/newsroom/press/release',
                            createdAt: '2025-04-26 06:27:39'
                        }
                    ]
                }
            },
            computed: {
                formatExecutionTime() {
                    return this.executionTime > 0 ? `${this.executionTime}ms` : '-';
                }
            },
            methods: {
                async executeQuery() {
                    this.loading = true;
                    this.hasSearched = true;

                    try {
                        // 调用后端API
                        const response = await axios.post('http://localhost:8080/api/query', {
                            searchKeyword: this.queryParams.searchKeyword,
                            searchFields: this.queryParams.searchFields,
                            limitCount: this.queryParams.limitCount,
                            orderBy: this.queryParams.orderBy,
                            tableName: this.queryParams.tableName
                        });

                        if (response.data.success) {
                            this.queryResults = response.data.data;
                            this.executionTime = response.data.meta.execution_time;

                            ElMessage.success(response.data.message);
                        } else {
                            ElMessage.error(response.data.message);
                            this.queryResults = [];
                        }

                    } catch (error) {
                        console.error('查询失败:', error);

                        // 如果API调用失败，使用模拟数据作为后备
                        if (error.code === 'ERR_NETWORK') {
                            ElMessage.warning('无法连接到服务器，使用模拟数据展示');
                            this.queryResults = this.mockData.filter(item =>
                                this.queryParams.searchFields.some(field =>
                                    item[field] && item[field].includes(this.queryParams.searchKeyword)
                                )
                            ).slice(0, this.queryParams.limitCount);
                            this.executionTime = 150; // 模拟执行时间
                        } else {
                            ElMessage.error('查询失败，请检查网络连接或参数设置');
                            this.queryResults = [];
                        }
                    } finally {
                        this.loading = false;
                    }
                },
                
                formatDate(dateString) {
                    if (!dateString) return '-';
                    return dateString; // 现在后端已经格式化为年月日
                },

                openUrl(url) {
                    if (url) {
                        window.open(url, '_blank');
                    }
                },

                async viewRecord(row) {
                    try {
                        // 调用API获取完整记录信息
                        const response = await axios.get(`http://localhost:8080/api/record/${row.id}?table=${this.queryParams.tableName}`);

                        if (response.data.success) {
                            this.currentRecord = response.data.data;
                            this.detailDialogVisible = true;
                        } else {
                            ElMessage.error('获取记录详情失败');
                        }
                    } catch (error) {
                        console.error('获取记录详情失败:', error);
                        // 如果API调用失败，使用当前行数据
                        this.currentRecord = row;
                        this.detailDialogVisible = true;
                    }
                },

                handleDetailClose() {
                    this.detailDialogVisible = false;
                    this.currentRecord = null;
                },

                getFieldDisplayName(key) {
                    const fieldNames = {
                        'id': 'ID',
                        'companyId': '公司ID',
                        'customer_id': '客户ID',
                        'MonitorDate': '监控日期',
                        'keywordShow': '关键词显示',
                        'keyword': '关键词',
                        'keywordRelation': '关键词关系',
                        'newsId': '新闻ID',
                        'title': '标题',
                        'content': '内容',
                        'newsSource': '新闻来源',
                        'page': '页码',
                        'type': '类型',
                        'url': '链接地址',
                        'createdAt': '创建时间'
                    };
                    return fieldNames[key] || key;
                },

                isHighlightField(key) {
                    return ['title', 'content', 'keyword'].includes(key);
                },

                isLongText(value) {
                    return typeof value === 'string' && value.length > 100;
                },

                exportToExcel() {
                    if (!this.currentRecord) return;

                    // 创建CSV格式的数据
                    let csvContent = "字段名,字段值\n";

                    for (const [key, value] of Object.entries(this.currentRecord)) {
                        const fieldName = this.getFieldDisplayName(key);
                        const fieldValue = (value || '').toString().replace(/"/g, '""'); // 转义双引号
                        csvContent += `"${fieldName}","${fieldValue}"\n`;
                    }

                    // 创建下载链接
                    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);
                    link.setAttribute('download', `记录详情_${this.currentRecord.id}_${new Date().toISOString().slice(0, 10)}.csv`);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    ElMessage.success('Excel文件已下载');
                }
            },
            
            mounted() {
                ElMessage.info('欢迎使用数据库查询系统！请设置查询参数后点击执行查询。');
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
