#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询系统启动文件
一键启动Flask后端服务和打开前端页面
"""

import os
import sys
import time
import subprocess
import webbrowser
import threading
from pathlib import Path

# 配置信息
CONFIG = {
    'host': '0.0.0.0',
    'port': 8080,
    'debug': True,
    'auto_open_browser': True,
    'browser_delay': 3  # 延迟3秒后打开浏览器
}

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🔍 数据库查询系统                          ║
║                  Database Query System                       ║
╠══════════════════════════════════════════════════════════════╣
║  功能特性:                                                    ║
║  ✅ 多关键字搜索 (逗号分隔)                                   ║
║  ✅ 智能关键字高亮                                            ║
║  ✅ Excel格式详情查看                                         ║
║  ✅ 参数化安全查询                                            ║
║  ✅ 美观的Vue.js界面                                          ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'flask',
        'flask_cors', 
        'pymysql',
        'tabulate'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} (未安装)")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)} -i https://pypi.tuna.tsinghua.edu.cn/simple")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def check_files():
    """检查必要文件是否存在"""
    print("\n📁 检查文件...")
    
    required_files = [
        'app.py',
        'index.html'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            missing_files.append(file)
            print(f"  ❌ {file} (不存在)")
    
    if missing_files:
        print(f"\n⚠️  缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有文件存在")
    return True

def test_database_connection():
    """测试数据库连接"""
    print("\n🔌 测试数据库连接...")
    
    try:
        import pymysql
        
        # 从app.py读取数据库配置
        db_config = {
            'host': '*************',
            'user': 'root',
            'password': 'Db1@neiwang',
            'database': 'compony_170',
            'port': 3306
        }
        
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        connection.close()
        
        if result:
            print("  ✅ 数据库连接成功")
            return True
        else:
            print("  ❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 数据库连接错误: {e}")
        return False

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(CONFIG['browser_delay'])
    url = f"http://localhost:{CONFIG['port']}"
    print(f"\n🌐 正在打开浏览器: {url}")
    try:
        webbrowser.open(url)
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")
        print(f"请手动访问: {url}")

def start_flask_server():
    """启动Flask服务器"""
    print(f"\n🚀 启动Flask服务器...")
    print(f"📍 地址: http://localhost:{CONFIG['port']}")
    print(f"🔧 调试模式: {'开启' if CONFIG['debug'] else '关闭'}")
    print("=" * 60)
    
    # 如果配置了自动打开浏览器，启动延迟线程
    if CONFIG['auto_open_browser']:
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
    
    # 启动Flask应用
    try:
        from app import app
        app.run(
            host=CONFIG['host'],
            port=CONFIG['port'],
            debug=CONFIG['debug']
        )
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

def show_usage_info():
    """显示使用说明"""
    info = f"""
📋 使用说明:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🌐 访问地址: http://localhost:{CONFIG['port']}

🔍 查询功能:
  • 多关键字搜索: 用逗号分隔多个关键词 (如: 美国,特朗普)
  • 字段选择: 可选择标题、内容、关键词等字段搜索
  • 智能高亮: 自动高亮匹配的关键词
  • 结果排序: 支持多种排序方式

📊 结果展示:
  • 表格显示: 清晰的数据表格展示
  • 详情查看: Excel格式的完整记录查看
  • 数据导出: 支持CSV格式导出

⚡ 快捷操作:
  • Ctrl+C: 停止服务器
  • F5: 刷新页面
  • Ctrl+Shift+I: 打开开发者工具

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    print(info)

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查文件
    if not check_files():
        sys.exit(1)
    
    # 测试数据库连接
    if not test_database_connection():
        print("\n⚠️  数据库连接失败，但仍可启动服务器（将使用模拟数据）")
        input("按回车键继续...")
    
    # 显示使用说明
    show_usage_info()
    
    # 启动服务器
    try:
        start_flask_server()
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
